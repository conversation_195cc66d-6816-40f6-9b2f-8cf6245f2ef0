"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
// Configuration
const inputFile = path.join(__dirname, '../../listing_0_1999.xml');
const outputDir = path.join(__dirname, '../../output');
const propertiesPerFile = 10;
const propertyRegex = /^\s*\d+\s+\w+\b/m;
// Logger
const log = (level, message) => {
    console.log(`${new Date().toISOString()} - ${level.toUpperCase()} - ${message}`);
};
// Ensure output directory exists
const ensureOutputDir = () => {
    if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
        log('info', `Created output directory: ${outputDir}`);
    }
};
// Read input file
const readInputFile = () => {
    try {
        const content = fs.readFileSync(inputFile, 'utf-8');
        log('info', `Successfully read input file: ${inputFile}`);
        return content;
    }
    catch (error) {
        if (error instanceof Error) {
            log('error', `Error reading input file: ${error.message}`);
        }
        process.exit(1);
    }
};
// Split properties
const splitProperties = (content) => {
    // Normalize line breaks and remove <DOCUMENT> wrapper
    content = content.replace(/\r\n|\r/g, '\n');
    const documentMatch = content.match(/<DOCUMENT[^>]*>([\s\S]*?)<\/DOCUMENT>/);
    if (!documentMatch) {
        log('error', 'No <DOCUMENT> wrapper found in input file.');
        process.exit(1);
    }
    content = documentMatch[1].trim();
    // Split into properties
    const lines = content.split('\n');
    const properties = [];
    let currentProperty = [];
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        if (propertyRegex.test(line)) {
            if (currentProperty.length) {
                properties.push(currentProperty.join('\n').trim());
                log('debug', `Property ${properties.length} detected with start: ${currentProperty[0].substring(0, 50)}...`);
            }
            currentProperty = [line];
        }
        else if (line.trim()) {
            currentProperty.push(line);
        }
        if (i % 1000 === 0) {
            log('info', `Processed ${i} lines`);
        }
    }
    if (currentProperty.length) {
        properties.push(currentProperty.join('\n').trim());
        log('debug', `Property ${properties.length} detected with start: ${currentProperty[0].substring(0, 50)}...`);
    }
    log('info', `Detected ${properties.length} property entries`);
    if (properties.length === 0) {
        log('error', 'No property entries detected. Check input file format.');
        process.exit(1);
    }
    else if (properties.length < 1000) {
        log('error', `Only ${properties.length} properties detected. Expected ~2000.`);
    }
    return properties;
};
// Write output files
const writeOutputFiles = (properties) => {
    for (let i = 0; i < properties.length; i += propertiesPerFile) {
        const chunk = properties.slice(i, i + propertiesPerFile);
        const fileName = `properties_${Math.floor(i / propertiesPerFile) + 1}.xml`;
        const outputPath = path.join(outputDir, fileName);
        const documentContent = `<DOCUMENT filename="${fileName}">\n${chunk.join('\n\n')}\n</DOCUMENT>`;
        try {
            fs.writeFileSync(outputPath, documentContent, 'utf-8');
            log('info', `Wrote ${chunk.length} properties to ${outputPath}`);
        }
        catch (error) {
            if (error instanceof Error) {
                log('error', `Error writing to ${outputPath}: ${error.message}`);
            }
        }
    }
};
// Main function
const main = () => {
    ensureOutputDir();
    const content = readInputFile();
    const properties = splitProperties(content);
    writeOutputFiles(properties);
    log('info', `Split ${properties.length} properties into ${Math.ceil(properties.length / propertiesPerFile)} files in '${outputDir}'`);
};
main();
