{"name": "ts_splitter", "version": "1.0.0", "main": "index.js", "scripts": {"build": "tsc", "dev": "nodemon --exec \"npm run build && node dist/index.js\" --ext ts --watch src", "start": "node dist/index.js", "clean": "rm -rf dist", "watch": "tsc --watch", "fix-images": "tsc && node dist/fix-image-urls.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "Tafadzwa Ma<PERSON>", "license": "ISC", "description": "", "devDependencies": {"@types/node": "^22.15.29", "@types/uuid": "^10.0.0", "nodemon": "^3.1.10", "typescript": "^5.8.3", "uuid": "^11.1.0"}}