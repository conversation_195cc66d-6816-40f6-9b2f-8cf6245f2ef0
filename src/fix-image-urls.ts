import * as fs from 'fs';
import * as path from 'path';

// Configuration
const outputDir: string = path.join(__dirname, '../output');
const baseUrl: string = 'https://goldengateasia.com/';

// Logger
const log = (level: 'info' | 'error' | 'success', message: string): void => {
  console.log(`${new Date().toISOString()} - ${level.toUpperCase()} - ${message}`);
};

// Fix image URLs in a single file
const fixImageUrlsInFile = (filePath: string): void => {
  try {
    // Read the file
    let content = fs.readFileSync(filePath, 'utf-8');
    
    // Count original occurrences
    const originalMatches = content.match(/<image>PropertyPics\//g);
    const originalCount = originalMatches ? originalMatches.length : 0;
    
    // Replace image paths that don't already have the base URL
    content = content.replace(
      /<image>PropertyPics\//g, 
      `<image>${baseUrl}PropertyPics/`
    );
    
    // Count new occurrences to verify the replacement
    const newMatches = content.match(new RegExp(`<image>${baseUrl.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}PropertyPics/`, 'g'));
    const newCount = newMatches ? newMatches.length : 0;
    
    // Write the file back
    fs.writeFileSync(filePath, content, 'utf-8');
    
    if (originalCount > 0) {
      log('success', `Fixed ${originalCount} image URLs in ${path.basename(filePath)}`);
    }
  } catch (error) {
    if (error instanceof Error) {
      log('error', `Error processing ${filePath}: ${error.message}`);
    }
  }
};

// Main function
const main = (): void => {
  log('info', `Starting to fix image URLs in ${outputDir}`);
  log('info', `Adding base URL: ${baseUrl}`);
  
  try {
    // Read all files in the output directory
    const files = fs.readdirSync(outputDir);
    const xmlFiles = files.filter(file => file.endsWith('.xml'));
    
    if (xmlFiles.length === 0) {
      log('error', 'No XML files found in output directory');
      return;
    }
    
    log('info', `Found ${xmlFiles.length} XML files to process`);
    
    let totalFilesProcessed = 0;
    let totalUrlsFixed = 0;
    
    // Process each XML file
    xmlFiles.forEach(fileName => {
      const filePath = path.join(outputDir, fileName);
      
      // Read and count before processing
      const beforeContent = fs.readFileSync(filePath, 'utf-8');
      const beforeMatches = beforeContent.match(/<image>PropertyPics\//g);
      const beforeCount = beforeMatches ? beforeMatches.length : 0;
      
      // Fix the file
      fixImageUrlsInFile(filePath);
      
      if (beforeCount > 0) {
        totalFilesProcessed++;
        totalUrlsFixed += beforeCount;
      }
    });
    
    log('success', `Processing complete!`);
    log('info', `Files processed: ${totalFilesProcessed}`);
    log('info', `Total image URLs fixed: ${totalUrlsFixed}`);
    
  } catch (error) {
    if (error instanceof Error) {
      log('error', `Error reading output directory: ${error.message}`);
    }
  }
};

main();
