import * as fs from 'fs';
import * as path from 'path';

// Configuration
const inputFile: string = path.join(__dirname, '../files/listing_0_1999.xml');
const outputDir: string = path.join(__dirname, '../output');
const propertiesPerFile: number = 10;

// Logger
const log = (level: 'info' | 'error' | 'debug', message: string): void => {
  console.log(`${new Date().toISOString()} - ${level.toUpperCase()} - ${message}`);
};

// Ensure output directory exists
const ensureOutputDir = (): void => {
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
    log('info', `Created output directory: ${outputDir}`);
  }
};

// Read input file
const readInputFile = (): string => {
  try {
    const content: string = fs.readFileSync(inputFile, 'utf-8');
    log('info', `Successfully read input file: ${inputFile}`);
    return content;
  } catch (error) {
    if (error instanceof Error) {
      log('error', `Error reading input file: ${error.message}`);
    }
    process.exit(1);
  }
};

// Split properties
const splitProperties = (content: string): string[] => {
  // Normalize line breaks
  content = content.replace(/\r\n|\r/g, '\n');

  // Check for <listings> wrapper
  const listingsMatch = content.match(/<listings[^>]*>([\s\S]*?)<\/listings>/i);
  if (!listingsMatch) {
    log('error', 'No <listings> wrapper found in input file.');
    process.exit(1);
  }

  const listingsContent = listingsMatch[1];

  // Extract all <Property> elements using regex
  const propertyMatches = listingsContent.match(/<Property[^>]*>[\s\S]*?<\/Property>/gi);

  if (!propertyMatches) {
    log('error', 'No <Property> elements found in input file.');
    process.exit(1);
  }

  const properties: string[] = propertyMatches.map((property, index) => {
    if ((index + 1) % 100 === 0) {
      log('info', `Processed ${index + 1} properties`);
    }
    return property.trim();
  });

  log('info', `Detected ${properties.length} property entries`);
  if (properties.length === 0) {
    log('error', 'No property entries detected. Check input file format.');
    process.exit(1);
  } else if (properties.length < 1000) {
    log('info', `Found ${properties.length} properties (less than expected ~2000, but continuing...)`);
  }

  return properties;
};

// Write output files
const writeOutputFiles = (properties: string[]): void => {
  for (let i = 0; i < properties.length; i += propertiesPerFile) {
    const chunk: string[] = properties.slice(i, i + propertiesPerFile);
    const fileName: string = `properties_${Math.floor(i / propertiesPerFile) + 1}.xml`;
    const outputPath: string = path.join(outputDir, fileName);
    const xmlContent: string = `<?xml version="1.0" encoding="UTF-8"?>\n<listings>\n${chunk.join('\n')}\n</listings>`;

    try {
      fs.writeFileSync(outputPath, xmlContent, 'utf-8');
      log('info', `Wrote ${chunk.length} properties to ${outputPath}`);
    } catch (error) {
      if (error instanceof Error) {
        log('error', `Error writing to ${outputPath}: ${error.message}`);
      }
    }
  }
};

// Main function
const main = (): void => {
  ensureOutputDir();
  const content: string = readInputFile();
  const properties: string[] = splitProperties(content);
  writeOutputFiles(properties);
  log('info', `Split ${properties.length} properties into ${Math.ceil(properties.length / propertiesPerFile)} files in '${outputDir}'`);
};

main();