import * as fs from 'fs';
import * as path from 'path';

// Configuration
const inputFile: string = path.join(__dirname, '../files/listing_0_1999.xml');
const outputDir: string = path.join(__dirname, '../../output');
const propertiesPerFile: number = 10;
const propertyRegex: RegExp = /^\s*\d+\s+\w+\b/m;

// Logger
const log = (level: 'info' | 'error' | 'debug', message: string): void => {
  console.log(`${new Date().toISOString()} - ${level.toUpperCase()} - ${message}`);
};

// Ensure output directory exists
const ensureOutputDir = (): void => {
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
    log('info', `Created output directory: ${outputDir}`);
  }
};

// Read input file
const readInputFile = (): string => {
  try {
    const content: string = fs.readFileSync(inputFile, 'utf-8');
    log('info', `Successfully read input file: ${inputFile}`);
    return content;
  } catch (error) {
    if (error instanceof Error) {
      log('error', `Error reading input file: ${error.message}`);
    }
    process.exit(1);
  }
};

// Split properties
const splitProperties = (content: string): string[] => {
  // Normalize line breaks and remove <DOCUMENT> wrapper
  content = content.replace(/\r\n|\r/g, '\n');
  const documentMatch = content.match(/<DOCUMENT[^>]*>([\s\S]*?)<\/DOCUMENT>/);
  if (!documentMatch) {
    log('error', 'No <DOCUMENT> wrapper found in input file.');
    process.exit(1);
  }
  content = documentMatch[1].trim();

  // Split into properties
  const lines: string[] = content.split('\n');
  const properties: string[] = [];
  let currentProperty: string[] = [];

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    if (propertyRegex.test(line)) {
      if (currentProperty.length) {
        properties.push(currentProperty.join('\n').trim());
        log('debug', `Property ${properties.length} detected with start: ${currentProperty[0].substring(0, 50)}...`);
      }
      currentProperty = [line];
    } else if (line.trim()) {
      currentProperty.push(line);
    }
    if (i % 1000 === 0) {
      log('info', `Processed ${i} lines`);
    }
  }

  if (currentProperty.length) {
    properties.push(currentProperty.join('\n').trim());
    log('debug', `Property ${properties.length} detected with start: ${currentProperty[0].substring(0, 50)}...`);
  }

  log('info', `Detected ${properties.length} property entries`);
  if (properties.length === 0) {
    log('error', 'No property entries detected. Check input file format.');
    process.exit(1);
  } else if (properties.length < 1000) {
    log('error', `Only ${properties.length} properties detected. Expected ~2000.`);
  }

  return properties;
};

// Write output files
const writeOutputFiles = (properties: string[]): void => {
  for (let i = 0; i < properties.length; i += propertiesPerFile) {
    const chunk: string[] = properties.slice(i, i + propertiesPerFile);
    const fileName: string = `properties_${Math.floor(i / propertiesPerFile) + 1}.xml`;
    const outputPath: string = path.join(outputDir, fileName);
    const documentContent: string = `<DOCUMENT filename="${fileName}">\n${chunk.join('\n\n')}\n</DOCUMENT>`;

    try {
      fs.writeFileSync(outputPath, documentContent, 'utf-8');
      log('info', `Wrote ${chunk.length} properties to ${outputPath}`);
    } catch (error) {
      if (error instanceof Error) {
        log('error', `Error writing to ${outputPath}: ${error.message}`);
      }
    }
  }
};

// Main function
const main = (): void => {
  ensureOutputDir();
  const content: string = readInputFile();
  const properties: string[] = splitProperties(content);
  writeOutputFiles(properties);
  log('info', `Split ${properties.length} properties into ${Math.ceil(properties.length / propertiesPerFile)} files in '${outputDir}'`);
};

main();